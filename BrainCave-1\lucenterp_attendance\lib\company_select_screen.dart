import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:lucenterp_attendance/Auth_screens/login_screen.dart';
// import 'package:shared_preferences/shared_preferences.dart';
// import 'package:url_launcher/url_launcher.dart';
// import 'package:flutter/gestures.dart';

class CompanySelectScreen extends StatefulWidget {
  const CompanySelectScreen({super.key});

  @override
  State<CompanySelectScreen> createState() => _CompanySelectScreenState();
}

class _CompanySelectScreenState extends State<CompanySelectScreen> {
  final Map<String, String> listofcompanies = {
    'Braincave Software': 'https://erp.braincavesoft.com/login',
    'Akrivis Engineering': 'https://erp.akrivisengineering.com/login',
    'Upasana Associate': 'https://erp.upasanaassociate.com/login',
  };

  // @override
  // void initState() {
  //   super.initState();
  //   _checkPreviouslySelectedCompany();
  // }

  // void _launchURL() async {
  //   final url = Uri.parse("https://lucenterp.com");
  //   if (await canLaunchUrl(url)) {
  //     await launchUrl(url, mode: LaunchMode.externalApplication);
  //   } else {
  //     throw 'Could not launch $url';
  //   }
  // }

  // Future<void> _checkPreviouslySelectedCompany() async {
  //   SharedPreferences prefs = await SharedPreferences.getInstance();
  //   String? companyName = prefs.getString('selected_company');
  //   String? companyUrl = prefs.getString('selected_company_url');

  //   if (companyName != null && companyUrl != null) {
  //     // Company already selected, go directly to WebViewScreen
  //     WidgetsBinding.instance.addPostFrameCallback((_) {
  //       Navigator.pushReplacement(
  //         context,
  //         MaterialPageRoute(
  //           builder:
  //               (context) => WebAppScreen(url: companyUrl, title: companyName),
  //         ),
  //       );
  //     });
  //   }
  // }

  // Future<void> _saveAndNavigate(MapEntry<String, String> entry) async {
  //   final navigator = Navigator.of(context);
  //   SharedPreferences prefs = await SharedPreferences.getInstance();
  //   await prefs.setString('selected_company', entry.key);
  //   await prefs.setString('selected_company_url', entry.value);

  //   if (!mounted) return;

  //   navigator.pushReplacement(
  //     MaterialPageRoute(
  //       builder: (context) => WebAppScreen(url: entry.value, title: entry.key),
  //     ),
  //   );
  // }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xFF561c24),
      body: Stack(
        children: [
          Center(
            child: ClipRRect(
              borderRadius: BorderRadius.circular(25),
              child: BackdropFilter(
                filter: ImageFilter.blur(sigmaX: 12.0, sigmaY: 12.0),
                child: Container(
                  width: MediaQuery.of(context).size.width * 0.88,
                  padding: const EdgeInsets.all(24),
                  decoration: BoxDecoration(
                    color: const Color(0xFFe8d8c4)..withAlpha(242),
                    borderRadius: BorderRadius.circular(25),
                    border: Border.all(color: Colors.white..withAlpha(64)),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black..withAlpha(38),
                        blurRadius: 10,
                        offset: const Offset(0, 5),
                      ),
                    ],
                  ),
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Image.asset(
                          'assets/images/lucent_erp2.png',
                          height: 180,
                          width: 180,
                        ),
                        const SizedBox(height: 24),

                        Text(
                          'Welcome to Lucent ERP',
                          style: GoogleFonts.poppins(
                            fontSize: 20,
                            fontWeight: FontWeight.w600,
                            color: Colors.black87,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 12),

                        // Instruction Text
                        Text(
                          'Please select your company name from the dropdown to proceed.',
                          style: GoogleFonts.poppins(
                            fontSize: 14.5,
                            fontWeight: FontWeight.w500,
                            color: Colors.black87,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 12),

                        Text(
                          'Note: Once selected, the company name cannot be changed unless you uninstall and reinstall the app. Choose carefully.',
                          style: GoogleFonts.poppins(
                            fontSize: 12.5,
                            fontStyle: FontStyle.italic,
                            color: Colors.red.shade700,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 24),

                        // Dropdown Field
                        DropdownButtonFormField<MapEntry<String, String>>(
                          hint: Text(
                            'Select your company',
                            style: GoogleFonts.poppins(
                              color: Colors.grey.shade700,
                            ),
                          ),
                          items: listofcompanies.entries.map((entry) {
                            return DropdownMenuItem<MapEntry<String, String>>(
                              value: entry,
                              child: Text(
                                entry.key,
                                style: GoogleFonts.poppins(
                                  fontSize: 15,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            );
                          }).toList(),

                          onChanged: (selectedEntry) {
                            if (selectedEntry != null) {
                              showDialog(
                                context: context,
                                builder: (BuildContext dialogContext) {
                                  return AlertDialog(
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(20),
                                    ),
                                    title: Text(
                                      'Confirm Selection',
                                      style: GoogleFonts.poppins(
                                        fontSize: 20,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                    content: Column(
                                      mainAxisSize: MainAxisSize.min,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        RichText(
                                          text: TextSpan(
                                            style: GoogleFonts.poppins(
                                              fontSize: 15,
                                              color: Colors.black,
                                            ),
                                            children: [
                                              const TextSpan(
                                                text:
                                                    'Are you sure you want to proceed with ',
                                              ),
                                              TextSpan(
                                                text: selectedEntry.key,
                                                style: const TextStyle(
                                                  fontWeight: FontWeight.bold,
                                                  // color: Colors.blue,
                                                ),
                                              ),
                                              const TextSpan(text: '?'),
                                            ],
                                          ),
                                        ),
                                        const SizedBox(height: 12),
                                        Text(
                                          'Please note: You won\'t be able to change it later. If everything is correct, please confirm to continue.',
                                          style: GoogleFonts.poppins(
                                            fontSize: 12.5,
                                            color: Colors.red.shade600,
                                          ),
                                        ),
                                      ],
                                    ),
                                    actionsPadding: const EdgeInsets.only(
                                      bottom: 12,
                                    ),

                                    actions: [
                                      Center(
                                        child: Row(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            ElevatedButton(
                                              style: ElevatedButton.styleFrom(
                                                backgroundColor: Color(
                                                  0xFFe8d8c4,
                                                )..withAlpha(242),
                                                shape: RoundedRectangleBorder(
                                                  borderRadius:
                                                      BorderRadius.circular(10),
                                                ),
                                              ),
                                              child: Text(
                                                'Cancel',
                                                style: GoogleFonts.poppins(
                                                  color: Color(0xFF561c24),
                                                ),
                                              ),
                                              onPressed: () {
                                                Navigator.pop(dialogContext);
                                              },
                                            ),
                                            const SizedBox(width: 16),
                                            ElevatedButton(
                                              style: ElevatedButton.styleFrom(
                                                backgroundColor: const Color(
                                                  0xFF561c24,
                                                ),
                                                shape: RoundedRectangleBorder(
                                                  borderRadius:
                                                      BorderRadius.circular(10),
                                                ),
                                              ),
                                              child: Text(
                                                'Confirm',
                                                style: GoogleFonts.poppins(
                                                  color: Colors.white,
                                                ),
                                              ),
                                              onPressed: () {
                                                Navigator.pop(dialogContext);
                                                Navigator.pushReplacement(
                                                  context,
                                                  MaterialPageRoute(
                                                    builder: (context) =>
                                                        LoginScreen(),
                                                  ),
                                                );
                                              },
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  );
                                },
                              );
                            }
                          },
                          decoration: InputDecoration(
                            filled: true,
                            fillColor: Colors.white,
                            contentPadding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 14,
                            ),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                              borderSide: const BorderSide(color: Colors.white),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                              borderSide: const BorderSide(
                                color: Color(0xFF561c24),
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(height: 12),

                        RichText(
                          textAlign: TextAlign.center,
                          text: TextSpan(
                            style: GoogleFonts.poppins(
                              fontSize: 13,
                              color: Colors.black87,
                              fontWeight: FontWeight.w500,
                            ),
                            children: [
                              const TextSpan(
                                text:
                                    "Don’t have an account or can’t find your company? ",
                              ),
                              const TextSpan(
                                text:
                                    "Please contact your company administrator or purchase a license ",
                              ),
                              TextSpan(
                                text: "here",
                                style: const TextStyle(
                                  color: Color(0xFF561c24),
                                  fontWeight: FontWeight.bold,
                                  fontSize: 14,
                                  decoration: TextDecoration.underline,
                                ),
                                // recognizer:
                                //     TapGestureRecognizer()..onTap = _launchURL,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
